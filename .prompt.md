this is a folder for the current project. Create a presentation timer web application with the following specifications:

1. Create a single HTML file that implements a sequential timer for managing presentation segments
2. Interface requirements:
   - Four input fields to define segment durations (in minutes)
   - Labels or descriptions for each segment
   - A prominent display showing current segment and remaining time
   - Clear start/pause/reset controls
   - Visual and text notifications when each segment ends
   - Clean, minimalist modern design using system fonts and subtle colors

3. Technical requirements:
   - Use vanilla HTML5, CSS3, and JavaScript only
   - Ensure cross-browser compatibility
   - Implement responsive design for different screen sizes
   - Include audio or visual alerts for segment transitions
   - Store segment times in browser localStorage
   - Display progress through both time and segments
   - Enable pause/resume functionality

4. User experience:
   - Clearly indicate active segment
   - Show upcoming segments
   - Provide visual feedback for time running out
   - Implement smooth transitions between segments
   - Display completion message after final segment

Please provide a self-contained solution optimized for modern browsers with graceful fallbacks. make it relatively small in terms of horizontal size, i want to embed it inside a wordpress page.