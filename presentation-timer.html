<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation Timer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        .timer-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 24px;
            max-width: 400px;
            width: 100%;
            border: 1px solid #e9ecef;
        }

        .header {
            text-align: center;
            margin-bottom: 24px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .segment-inputs {
            margin-bottom: 24px;
        }

        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            gap: 12px;
        }

        .input-group label {
            flex: 1;
            font-size: 14px;
            color: #495057;
            font-weight: 500;
        }

        .input-group input {
            width: 80px;
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            text-align: center;
            transition: border-color 0.2s;
        }

        .input-group input:focus {
            outline: none;
            border-color: #007bff;
        }

        .timer-display {
            text-align: center;
            margin-bottom: 24px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }

        .current-segment {
            font-size: 16px;
            color: #6c757d;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .time-remaining {
            font-size: 48px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 12px;
            font-variant-numeric: tabular-nums;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .segment-progress {
            display: flex;
            gap: 4px;
            margin-bottom: 16px;
        }

        .segment-dot {
            flex: 1;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            transition: background-color 0.3s;
        }

        .segment-dot.completed {
            background: #28a745;
        }

        .segment-dot.active {
            background: #007bff;
        }

        .controls {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .notification {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-weight: 500;
            text-align: center;
            display: none;
        }

        .notification.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .notification.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .notification.show {
            display: block;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .warning {
            color: #dc3545 !important;
        }

        .warning .progress-fill {
            background: linear-gradient(90deg, #dc3545, #c82333);
        }

        @media (max-width: 480px) {
            .timer-container {
                padding: 16px;
                margin: 10px;
            }
            
            .time-remaining {
                font-size: 36px;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="timer-container">
        <div class="header">
            <h1>Presentation Timer</h1>
        </div>

        <div class="segment-inputs">
            <div class="input-group">
                <label for="segment1">Introduction:</label>
                <input type="number" id="segment1" min="1" max="60" value="5" placeholder="5">
                <span>min</span>
            </div>
            <div class="input-group">
                <label for="segment2">Main Content:</label>
                <input type="number" id="segment2" min="1" max="60" value="15" placeholder="15">
                <span>min</span>
            </div>
            <div class="input-group">
                <label for="segment3">Q&A Session:</label>
                <input type="number" id="segment3" min="1" max="60" value="8" placeholder="8">
                <span>min</span>
            </div>
            <div class="input-group">
                <label for="segment4">Conclusion:</label>
                <input type="number" id="segment4" min="1" max="60" value="2" placeholder="2">
                <span>min</span>
            </div>
        </div>

        <div class="notification" id="notification"></div>

        <div class="timer-display">
            <div class="current-segment" id="currentSegment">Ready to start</div>
            <div class="time-remaining" id="timeRemaining">00:00</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="segment-progress" id="segmentProgress">
                <div class="segment-dot"></div>
                <div class="segment-dot"></div>
                <div class="segment-dot"></div>
                <div class="segment-dot"></div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" id="startPauseBtn">Start</button>
            <button class="btn btn-secondary" id="resetBtn">Reset</button>
        </div>
    </div>

    <script>
        class PresentationTimer {
            constructor() {
                this.segments = [];
                this.currentSegmentIndex = 0;
                this.timeRemaining = 0;
                this.totalTime = 0;
                this.isRunning = false;
                this.isPaused = false;
                this.interval = null;
                
                this.initializeElements();
                this.loadFromStorage();
                this.bindEvents();
                this.updateDisplay();
            }

            initializeElements() {
                this.segmentInputs = [
                    document.getElementById('segment1'),
                    document.getElementById('segment2'),
                    document.getElementById('segment3'),
                    document.getElementById('segment4')
                ];
                this.currentSegmentEl = document.getElementById('currentSegment');
                this.timeRemainingEl = document.getElementById('timeRemaining');
                this.progressFillEl = document.getElementById('progressFill');
                this.segmentProgressEl = document.getElementById('segmentProgress');
                this.startPauseBtn = document.getElementById('startPauseBtn');
                this.resetBtn = document.getElementById('resetBtn');
                this.notificationEl = document.getElementById('notification');
            }

            bindEvents() {
                this.startPauseBtn.addEventListener('click', () => this.toggleTimer());
                this.resetBtn.addEventListener('click', () => this.resetTimer());
                
                this.segmentInputs.forEach(input => {
                    input.addEventListener('change', () => this.saveToStorage());
                });
            }

            loadFromStorage() {
                const saved = localStorage.getItem('presentationTimer');
                if (saved) {
                    const data = JSON.parse(saved);
                    this.segmentInputs.forEach((input, index) => {
                        if (data.segments[index]) {
                            input.value = data.segments[index];
                        }
                    });
                }
            }

            saveToStorage() {
                const data = {
                    segments: this.segmentInputs.map(input => parseInt(input.value) || 0)
                };
                localStorage.setItem('presentationTimer', JSON.stringify(data));
            }

            getSegments() {
                return this.segmentInputs.map(input => parseInt(input.value) || 0);
            }

            formatTime(seconds) {
                const mins = Math.floor(seconds / 60);
                const secs = seconds % 60;
                return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }

            showNotification(message, type = 'success') {
                this.notificationEl.textContent = message;
                this.notificationEl.className = `notification ${type} show`;
                setTimeout(() => {
                    this.notificationEl.classList.remove('show');
                }, 3000);
            }

            playAlert() {
                // Create audio context for beep sound
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    
                    oscillator.frequency.value = 800;
                    oscillator.type = 'sine';
                    
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                    
                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.5);
                } catch (e) {
                    console.log('Audio not supported');
                }
            }

            updateDisplay() {
                const segments = this.getSegments();
                const segmentLabels = ['Introduction', 'Main Content', 'Q&A Session', 'Conclusion'];
                
                if (!this.isRunning && !this.isPaused) {
                    this.currentSegmentEl.textContent = 'Ready to start';
                    this.timeRemainingEl.textContent = '00:00';
                    this.progressFillEl.style.width = '0%';
                } else {
                    this.currentSegmentEl.textContent = segmentLabels[this.currentSegmentIndex];
                    this.timeRemainingEl.textContent = this.formatTime(this.timeRemaining);
                    
                    const currentSegmentTime = segments[this.currentSegmentIndex] * 60;
                    const progress = ((currentSegmentTime - this.timeRemaining) / currentSegmentTime) * 100;
                    this.progressFillEl.style.width = `${Math.max(0, Math.min(100, progress))}%`;
                    
                    // Warning when less than 30 seconds remaining
                    if (this.timeRemaining <= 30 && this.timeRemaining > 0) {
                        this.timeRemainingEl.classList.add('warning');
                        this.progressFillEl.parentElement.classList.add('warning');
                    } else {
                        this.timeRemainingEl.classList.remove('warning');
                        this.progressFillEl.parentElement.classList.remove('warning');
                    }
                }
                
                // Update segment progress dots
                const dots = this.segmentProgressEl.children;
                for (let i = 0; i < dots.length; i++) {
                    dots[i].className = 'segment-dot';
                    if (i < this.currentSegmentIndex) {
                        dots[i].classList.add('completed');
                    } else if (i === this.currentSegmentIndex && this.isRunning) {
                        dots[i].classList.add('active');
                    }
                }
            }

            toggleTimer() {
                if (!this.isRunning && !this.isPaused) {
                    this.startTimer();
                } else if (this.isRunning) {
                    this.pauseTimer();
                } else if (this.isPaused) {
                    this.resumeTimer();
                }
            }

            startTimer() {
                this.segments = this.getSegments();
                if (this.segments.every(s => s === 0)) {
                    this.showNotification('Please set at least one segment duration', 'warning');
                    return;
                }
                
                this.saveToStorage();
                this.currentSegmentIndex = 0;
                this.timeRemaining = this.segments[0] * 60;
                this.isRunning = true;
                this.isPaused = false;
                
                this.startPauseBtn.textContent = 'Pause';
                this.segmentInputs.forEach(input => input.disabled = true);
                
                this.interval = setInterval(() => this.tick(), 1000);
                this.updateDisplay();
                this.showNotification('Timer started!');
            }

            pauseTimer() {
                this.isRunning = false;
                this.isPaused = true;
                clearInterval(this.interval);
                this.startPauseBtn.textContent = 'Resume';
                this.showNotification('Timer paused');
            }

            resumeTimer() {
                this.isRunning = true;
                this.isPaused = false;
                this.startPauseBtn.textContent = 'Pause';
                this.interval = setInterval(() => this.tick(), 1000);
                this.showNotification('Timer resumed');
            }

            resetTimer() {
                this.isRunning = false;
                this.isPaused = false;
                this.currentSegmentIndex = 0;
                this.timeRemaining = 0;
                
                clearInterval(this.interval);
                this.startPauseBtn.textContent = 'Start';
                this.segmentInputs.forEach(input => input.disabled = false);
                
                this.updateDisplay();
                this.showNotification('Timer reset');
            }

            tick() {
                this.timeRemaining--;
                
                if (this.timeRemaining <= 0) {
                    this.playAlert();
                    
                    if (this.currentSegmentIndex < this.segments.length - 1) {
                        const segmentLabels = ['Introduction', 'Main Content', 'Q&A Session', 'Conclusion'];
                        this.showNotification(`${segmentLabels[this.currentSegmentIndex]} completed!`);
                        
                        this.currentSegmentIndex++;
                        this.timeRemaining = this.segments[this.currentSegmentIndex] * 60;
                    } else {
                        this.isRunning = false;
                        clearInterval(this.interval);
                        this.startPauseBtn.textContent = 'Start';
                        this.segmentInputs.forEach(input => input.disabled = false);
                        this.showNotification('Presentation completed! 🎉');
                        this.currentSegmentEl.textContent = 'Presentation Complete';
                        this.timeRemainingEl.textContent = '00:00';
                        return;
                    }
                }
                
                this.updateDisplay();
            }
        }

        // Initialize the timer when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new PresentationTimer();
        });
    </script>
</body>
</html>
